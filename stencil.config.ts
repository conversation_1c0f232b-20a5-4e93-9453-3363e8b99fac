import type { Config } from '@stencil/core';
import { reactOutputTarget } from '@stencil/react-output-target';
import tailwind, { tailwindHMR } from 'stencil-tailwind-plugin';

export const config: Config = {
  namespace: 'gahr',
  plugins: [
    tailwind({
      tailwindCssPath: './src/styles/main.css',
    }),
    tailwindHMR(),
  ],
  devServer: {
    reloadStrategy: 'pageReload',
  },
  outputTargets: [
    {
      type: 'dist',
      esmLoaderPath: '../dist/loader',
      empty: false,
      copy: [
        { src: './assets/fonts', dest: 'assets/fonts' },
      ]
    },
    {
      type: 'dist-custom-elements',
      dir: './dist/components',
      customElementsExportBehavior: 'auto-define-custom-elements',
      externalRuntime: false,
    },
    {
      type: 'docs-readme',
    },
    {
      type: 'www',
      serviceWorker: null,
      copy: [
        { src: './docs' },
        { src: './assets/fonts', dest: 'assets/fonts' },
        { src: './components/monogram/assets', dest: 'assets' },
        { src: './components/logo/assets', dest: 'assets' },	
      ]
    },
    {
      type: 'dist-hydrate-script',
      dir: './dist/hydrate',
    },
    reactOutputTarget({
      outDir: './dist/react',
      hydrateModule: '../hydrate',
      clientModule: 'gahr-design-system-react',
      stencilPackageName: '.',
      customElementsDir: '../components',
    }),
  ],
  testing: {
    browserHeadless: "shell",
  },
};
