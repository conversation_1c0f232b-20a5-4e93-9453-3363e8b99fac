import { format, getBrandName, getClosestDataTheme } from './utils';

describe('format', () => {
  it('returns empty string for no names defined', () => {
    expect(format(undefined, undefined, undefined)).toEqual('');
  });

  it('formats just first names', () => {
    expect(format('<PERSON>', undefined, undefined)).toEqual('<PERSON>');
  });

  it('formats first and last names', () => {
    expect(format('<PERSON>', undefined, 'Publique')).toEqual('Joseph Publique');
  });

  it('formats first, middle and last names', () => {
    expect(format('<PERSON>', 'Quincy', 'Publique')).toEqual('Joseph Quincy Publique');
  });
});


describe('getBrandName', () => {
  it('returns Grand America for ga', () => {
    expect(getBrandName('ga')).toEqual('Grand America');
  });

  it('returns Little America for la', () => {
    expect(getBrandName('la')).toEqual('Little America');
  });
});


describe('getClosestDataTheme', () => {
  let mockElement: Element;
  let mockParent: Element;
  let mockGrandparent: Element;
  let mockDocumentElement: Element;

  beforeEach(() => {
    // Create mock elements for testing
    mockElement = document.createElement('div');
    mockParent = document.createElement('div');
    mockGrandparent = document.createElement('div');
    mockDocumentElement = document.createElement('html');
    
    // Set up DOM hierarchy
    mockGrandparent.appendChild(mockParent);
    mockParent.appendChild(mockElement);
    
    // Mock document.documentElement
    Object.defineProperty(document, 'documentElement', {
      value: mockDocumentElement,
      writable: true
    });
  });

  afterEach(() => {
    // Clean up mocks
    jest.clearAllMocks();
  });

  it('returns data-theme from current element when present', () => {
    mockElement.setAttribute('data-theme', 'la');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('la');
  });

  it('returns data-theme from parent element when current element has none', () => {
    mockParent.setAttribute('data-theme', 'ga');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('ga');
  });

  it('returns data-theme from grandparent element when closer elements have none', () => {
    mockGrandparent.setAttribute('data-theme', 'la');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('la');
  });

  it('returns document root data-theme when no element in tree has it', () => {
    mockDocumentElement.setAttribute('data-theme', 'ga');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('ga');
  });

  it('returns default "ga" when no data-theme found anywhere', () => {
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('ga');
  });

  it('stops traversing after max depth to prevent infinite loops', () => {
    // Create a deep chain of elements without data-theme
    let currentElement = mockElement;
    for (let i = 0; i < 15; i++) {
      const newElement = document.createElement('div');
      newElement.appendChild(currentElement);
      currentElement = newElement;
    }
    
    // Set data-theme on the deepest element (beyond max depth)
    currentElement.setAttribute('data-theme', 'la');
    
    const result = getClosestDataTheme(mockElement);
    
    // Should not find the deep data-theme, should fall back to default
    expect(result).toBe('ga');
  });

  it('handles null parentElement gracefully', () => {
    // Mock parentElement to return null
    Object.defineProperty(mockElement, 'parentElement', {
      value: null,
      writable: true
    });
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('ga');
  });

  it('prioritizes closest element with data-theme over deeper ones', () => {
    mockElement.setAttribute('data-theme', 'la');
    mockParent.setAttribute('data-theme', 'ga');
    mockGrandparent.setAttribute('data-theme', 'la');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('la'); // Should get the closest one
  });

  it('works with different data-theme values', () => {
    mockElement.setAttribute('data-theme', 'custom-theme');
    
    const result = getClosestDataTheme(mockElement);
    
    expect(result).toBe('custom-theme');
  });
}); 