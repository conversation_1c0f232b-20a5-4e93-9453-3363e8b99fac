export function format(first?: string, middle?: string, last?: string): string {
  return (first || '') + (middle ? ` ${middle}` : '') + (last ? ` ${last}` : '');
}

export function getBrandName(brand: 'ga' | 'la-slc'): string {
  return brand === 'ga' ? 'Grand America' : 'Little America';
}

export function getClosestDataTheme(currentElement: Element): string {
    let depth = 0;
    const maxDepth = 10; // Prevent infinite loops
    
    // Traverse up the DOM tree until we find a data-theme attribute
    while (currentElement && depth < maxDepth) {
      const theme = currentElement.getAttribute('data-theme');
      if (theme) {
        return theme;
      }
      
      // Move to the parent element
      currentElement = currentElement.parentElement;
      depth++;
    }
    
    // If no data-theme found in the tree, fall back to document root
    const rootTheme = document.documentElement.getAttribute('data-theme');
    if (rootTheme) {
      return rootTheme;
    }    
    return 'ga';
}
