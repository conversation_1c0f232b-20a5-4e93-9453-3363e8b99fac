<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Monogram Component - GAHR Design System</title>
    <script type="module" src="/build/gahr.esm.js"></script>
    <script nomodule src="/build/gahr.js"></script>
    <link rel="stylesheet" href="/styles/main.css" />
    <link rel="stylesheet" href="./style.css" />
  </head>
  <body>
    <gahr-page-content>
      <div class="container mx-auto p-4">
        <gahr-rich-text>    
          <h1>Monogram Component</h1>
          <p>
            The Monogram component displays brand monograms for Grand America Hotel and Little America Salt Lake City. It supports primary and secondary variants per brand with
            configurable sizing. Unlike the full logo, monograms do not have hover-state or mobile-specific assets.
          </p>
        </gahr-rich-text>

        <div class="demo-section" data-theme="ga">
          <h2>Grand America Hotel Monograms</h2>
          <div class="demo-grid">
            <div class="demo-item-gray" style="width: 150px;">
              <gahr-monogram/>
            </div>

            <div class="demo-item-gray" style="width: 200px;">
              <gahr-monogram/>
            </div>
          </div>
        </div>

        <div class="demo-section" data-theme="la-slc">
          <h2>Little America Salt Lake City Monograms</h2>
          <div class="demo-grid">
            <div class="demo-item-gray" style="width: 150px;">
              <gahr-monogram/>
            </div>

            <div class="demo-item-gray" style="width: 200px;">
              <gahr-monogram/>
            </div>
          </div>
        </div>
      </div>
    </gahr-page-content>
  </body>
</html>

