<!DOCTYPE html>
<html dir="ltr" lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0" />
    <title>GAHR Design System</title>

    <script type="module" src="/build/gahr.esm.js"></script>
    <script nomodule src="/build/gahr.js"></script>
  </head>
  <body>
    <gahr-page-content>
      <div class="container mx-auto p-4">

        <gahr-rich-text>
          <h1>Button</h1>
          <p>This is an example of using the button component.</p>
        </gahr-rich-text>

        <gahr-button onclick="console.log('button clicked')">Button Text</gahr-button>

      </div>
    </gahr-page-content>
  </body>
</html>
