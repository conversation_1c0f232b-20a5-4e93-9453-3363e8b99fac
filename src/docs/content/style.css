.color-wrapper {
  display: flex;
  align-items: center;
}

.color-wrapper p {
  margin: 0 4px;
}

.color-sample {
  border: 1px solid #000000;
  height: 64px;
  width: 64px;
  margin-right: 4px;
}

.primary-color-sample {
  background-color: var(--color-primary);
}

.secondary-color-sample {
  background-color: var(--color-secondary);
}

.primary-content-color-sample {
  background-color: var(--color-primary-content);
}

.secondary-content-color-sample {
  background-color: var(--color-secondary-content);
}

.font-serif-example-bold {
  font-family: var(--font-serif);
  font-weight: 700;
  font-size: 2.5rem;
  line-height: 1.2;
}

.font-serif-example-semibold {
  font-family: var(--font-serif);
  font-weight: 600;
  font-size: 2rem;
  line-height: 1.3;
}

.font-serif-example-regular {
  font-family: var(--font-serif);
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 1.4;
}

.font-serif-example-italic {
  font-family: var(--font-serif);
  font-weight: 400;
  font-size: 1.5rem;
  line-height: 1.4;
  font-style: italic;
}

.font-serif-example-light {
  font-family: var(--font-serif);
  font-weight: 300;
  font-size: 1.5rem;
  line-height: 1.4;
}

.font-serif-example-light-italic {
  font-family: var(--font-serif);
  font-weight: 300;
  font-size: 1.5rem;
  line-height: 1.4;
  font-style: italic;
}

.font-sans-example-bold {
  font-family: var(--font-sans);
  font-weight: 700;
  font-size: 1.25rem;
  line-height: 1.2;
}

.font-sans-example-regular {
  font-family: var(--font-sans);
  font-weight: 400;
}