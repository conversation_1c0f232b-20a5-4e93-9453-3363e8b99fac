/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { NavItem } from "./components/page-header/page-header";
export { NavItem } from "./components/page-header/page-header";
export namespace Components {
    interface GahrAccordion {
    }
    interface GahrAccordionSection {
        /**
          * Accordion Name This is used to group the accordion sections together.
         */
        "accordionName": string;
        /**
          * Is Open This determines if the accordion section is open by default.
         */
        "isOpen": boolean;
        /**
          * Section Title This is the title of the accordion section that will be displayed.
         */
        "sectionTitle": string;
    }
    interface GahrButton {
    }
    interface GahrLink {
        /**
          * Link Href This is the URL that the link points to.
         */
        "href": string;
        /**
          * Link Class This is the class applied to the link element.
         */
        "linkClass": string;
        /**
          * Link Type This is the type of link to render.
         */
        "linkType": string;
    }
    interface GahrLogo {
        /**
          * CSS class to apply to the logo
         */
        "class"?: string;
    }
    interface GahrMonogram {
        /**
          * Alt text for accessibility
          * @default 'Monogram'
         */
        "alt"?: string;
        /**
          * CSS class to apply to the monogram
         */
        "class"?: string;
    }
    interface GahrPageContent {
    }
    interface GahrPageFooter {
    }
    interface GahrPageHeader {
        /**
          * @default defaultNav
         */
        "primaryNav": NavItem[];
    }
    interface GahrRichText {
    }
}
declare global {
    interface HTMLGahrAccordionElement extends Components.GahrAccordion, HTMLStencilElement {
    }
    var HTMLGahrAccordionElement: {
        prototype: HTMLGahrAccordionElement;
        new (): HTMLGahrAccordionElement;
    };
    interface HTMLGahrAccordionSectionElement extends Components.GahrAccordionSection, HTMLStencilElement {
    }
    var HTMLGahrAccordionSectionElement: {
        prototype: HTMLGahrAccordionSectionElement;
        new (): HTMLGahrAccordionSectionElement;
    };
    interface HTMLGahrButtonElement extends Components.GahrButton, HTMLStencilElement {
    }
    var HTMLGahrButtonElement: {
        prototype: HTMLGahrButtonElement;
        new (): HTMLGahrButtonElement;
    };
    interface HTMLGahrLinkElement extends Components.GahrLink, HTMLStencilElement {
    }
    var HTMLGahrLinkElement: {
        prototype: HTMLGahrLinkElement;
        new (): HTMLGahrLinkElement;
    };
    interface HTMLGahrLogoElement extends Components.GahrLogo, HTMLStencilElement {
    }
    var HTMLGahrLogoElement: {
        prototype: HTMLGahrLogoElement;
        new (): HTMLGahrLogoElement;
    };
    interface HTMLGahrMonogramElement extends Components.GahrMonogram, HTMLStencilElement {
    }
    var HTMLGahrMonogramElement: {
        prototype: HTMLGahrMonogramElement;
        new (): HTMLGahrMonogramElement;
    };
    interface HTMLGahrPageContentElement extends Components.GahrPageContent, HTMLStencilElement {
    }
    var HTMLGahrPageContentElement: {
        prototype: HTMLGahrPageContentElement;
        new (): HTMLGahrPageContentElement;
    };
    interface HTMLGahrPageFooterElement extends Components.GahrPageFooter, HTMLStencilElement {
    }
    var HTMLGahrPageFooterElement: {
        prototype: HTMLGahrPageFooterElement;
        new (): HTMLGahrPageFooterElement;
    };
    interface HTMLGahrPageHeaderElement extends Components.GahrPageHeader, HTMLStencilElement {
    }
    var HTMLGahrPageHeaderElement: {
        prototype: HTMLGahrPageHeaderElement;
        new (): HTMLGahrPageHeaderElement;
    };
    interface HTMLGahrRichTextElement extends Components.GahrRichText, HTMLStencilElement {
    }
    var HTMLGahrRichTextElement: {
        prototype: HTMLGahrRichTextElement;
        new (): HTMLGahrRichTextElement;
    };
    interface HTMLElementTagNameMap {
        "gahr-accordion": HTMLGahrAccordionElement;
        "gahr-accordion-section": HTMLGahrAccordionSectionElement;
        "gahr-button": HTMLGahrButtonElement;
        "gahr-link": HTMLGahrLinkElement;
        "gahr-logo": HTMLGahrLogoElement;
        "gahr-monogram": HTMLGahrMonogramElement;
        "gahr-page-content": HTMLGahrPageContentElement;
        "gahr-page-footer": HTMLGahrPageFooterElement;
        "gahr-page-header": HTMLGahrPageHeaderElement;
        "gahr-rich-text": HTMLGahrRichTextElement;
    }
}
declare namespace LocalJSX {
    interface GahrAccordion {
    }
    interface GahrAccordionSection {
        /**
          * Accordion Name This is used to group the accordion sections together.
         */
        "accordionName"?: string;
        /**
          * Is Open This determines if the accordion section is open by default.
         */
        "isOpen"?: boolean;
        /**
          * Section Title This is the title of the accordion section that will be displayed.
         */
        "sectionTitle"?: string;
    }
    interface GahrButton {
    }
    interface GahrLink {
        /**
          * Link Href This is the URL that the link points to.
         */
        "href"?: string;
        /**
          * Link Class This is the class applied to the link element.
         */
        "linkClass"?: string;
        /**
          * Link Type This is the type of link to render.
         */
        "linkType"?: string;
    }
    interface GahrLogo {
        /**
          * CSS class to apply to the logo
         */
        "class"?: string;
    }
    interface GahrMonogram {
        /**
          * Alt text for accessibility
          * @default 'Monogram'
         */
        "alt"?: string;
        /**
          * CSS class to apply to the monogram
         */
        "class"?: string;
    }
    interface GahrPageContent {
    }
    interface GahrPageFooter {
    }
    interface GahrPageHeader {
        /**
          * @default defaultNav
         */
        "primaryNav"?: NavItem[];
    }
    interface GahrRichText {
    }
    interface IntrinsicElements {
        "gahr-accordion": GahrAccordion;
        "gahr-accordion-section": GahrAccordionSection;
        "gahr-button": GahrButton;
        "gahr-link": GahrLink;
        "gahr-logo": GahrLogo;
        "gahr-monogram": GahrMonogram;
        "gahr-page-content": GahrPageContent;
        "gahr-page-footer": GahrPageFooter;
        "gahr-page-header": GahrPageHeader;
        "gahr-rich-text": GahrRichText;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "gahr-accordion": LocalJSX.GahrAccordion & JSXBase.HTMLAttributes<HTMLGahrAccordionElement>;
            "gahr-accordion-section": LocalJSX.GahrAccordionSection & JSXBase.HTMLAttributes<HTMLGahrAccordionSectionElement>;
            "gahr-button": LocalJSX.GahrButton & JSXBase.HTMLAttributes<HTMLGahrButtonElement>;
            "gahr-link": LocalJSX.GahrLink & JSXBase.HTMLAttributes<HTMLGahrLinkElement>;
            "gahr-logo": LocalJSX.GahrLogo & JSXBase.HTMLAttributes<HTMLGahrLogoElement>;
            "gahr-monogram": LocalJSX.GahrMonogram & JSXBase.HTMLAttributes<HTMLGahrMonogramElement>;
            "gahr-page-content": LocalJSX.GahrPageContent & JSXBase.HTMLAttributes<HTMLGahrPageContentElement>;
            "gahr-page-footer": LocalJSX.GahrPageFooter & JSXBase.HTMLAttributes<HTMLGahrPageFooterElement>;
            "gahr-page-header": LocalJSX.GahrPageHeader & JSXBase.HTMLAttributes<HTMLGahrPageHeaderElement>;
            "gahr-rich-text": LocalJSX.GahrRichText & JSXBase.HTMLAttributes<HTMLGahrRichTextElement>;
        }
    }
}
