@import "tailwindcss";

@plugin "@tailwindcss/typography";

/*
  Grand America Font Definitions
*/
@font-face {
  font-family: 'Chronicle Display';
  src: url('/assets/fonts/ChronicleDisplay-Roman.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chronicle Display';
  src: url('/assets/fonts/ChronicleDisplay-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chronicle Display';
  src: url('/assets/fonts/ChronicleDisplay-Semi.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Chronicle Display';
  src: url('/assets/fonts/ChronicleDisplay-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'MrEaves Sans';
  src: url('/assets/fonts/mreavessanot-light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MrEaves Sans';
  src: url('/assets/fonts/mreavessanot-book.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MrEaves Sans';
  src: url('/assets/fonts/mreavessanot-bookitalic.ttf') format('truetype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'MrEaves Sans';
  src: url('/assets/fonts/mreavessanot-bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}


@plugin "daisyui" {
  themes: false;
}

/*
  Grand America Theme (Default)
*/
@plugin "daisyui/theme" {
  name: "ga";
  default: true;
  prefersdark: false;
  color-scheme: light;

  --root-bg: var(--color-white);

  --color-primary: #90722C;
  --color-primary-content: #FFFFFF;
  --color-secondary: #E1DFD3;
  --color-secondary-content: #000000;

  --color-accent: #808289;
  --color-accent-content: #000000;

  --color-base-100: #FFFFFF;
  --color-base-200: #F9F6F1;
  --color-base-content: #000000;

  --color-error: #EC1C24;
  --color-error-content: #F9F6F1; 

  --color-black: #000000;
  --color-white: #FFFFFF;

  --color-button-primary: #90722C;
  --color-button-primary-content: #FFFFFF;
  --color-button-inverted: #E1DFD3;
  --color-button-inverted-content: #000000;  
  --color-button-dark: #5F4C26;
  --color-button-dark-content: #FFFFFF;
  --line-height: 140%;  
  
  /* 
  --color-neutral: #000000;
  --color-neutral-content: #FFFFFF;
  --color-base-300: #C9C6C6;
  --color-info: #5F4C26;
  --color-info-content: #F9F6F1;
  --color-success: #5F4C26;
  --color-success-content: #F9F6F1;
  --color-warning: #5F4C26;
  --color-warning-content: #F9F6F1;
  */
}

/* 
Little America Font Definitions 
*/

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-LightItalic.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-Book.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-BookItalic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'CenturyStd';
  src: url('/assets/fonts/CenturyStd-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

/* Trade Gothic Font Definitions */
@font-face {
  font-family: 'Trade Gothic';
  src: url('/assets/fonts/trade-gothic-lt-std.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Trade Gothic';
  src: url('/assets/fonts/trade-gothic-lt-std-bold-condensed-no-20.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}


/*
  Little America - SLC Theme
*/
@plugin "daisyui/theme" {
  name: "la-slc";
  default: false;
  prefersdark: false;
  color-scheme: light;

  --root-bg: var(--color-white);
  --color-base-content: var(--color-gray-800);

  --color-primary: #74272A;
  --color-primary-content: #FFFFFF;
  --color-secondary: #D6D1CA;
  --color-secondary-content: #000000;

  --color-accent: #808289;
  --color-accent-content: #000000;

  --color-base-100: #FFFFFF;
  --color-base-200: #F8F4F5;
  --color-base-content: #000000;

  --color-error: #EC1C24;
  --color-error-content: #F9F6F1;   

  --color-black: #000000;
  --color-white: #FFFFFF;

  --color-button-primary: #74272A;
  --color-button-primary-content: #FFFFFF;
  --color-button-inverted: #D6D1CA;
  --color-button-inverted-content: #000000;
  --color-button-dark: #74272A;
  --color-button-dark-content: #FFFFFF;

  --line-height: 130%;
  --padding-button: 14px 16px 11px 16px;

}

[data-theme="ga"] {
  --font-serif: 'Chronicle Display', Georgia, 'Times New Roman', serif;
  --font-sans: 'MrEaves Sans', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

[data-theme="la-slc"] {
  --font-serif: 'CenturyStd', Georgia, 'Times New Roman', serif;
  --font-sans: 'Trade Gothic', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
} 

/*
  Force the following classes to be included in the final CSS build.
*/
@source inline('{inline,inline-block,block,flex,grid,hidden,sr-only}');
@source inline("{w-full,max-w-none}");
@source inline("{m,p}{x,y,t,b,l,r,}-{{1..10},auto}");
@source inline('{sm:,md:,lg:,xl:,}grid-cols-{{1..12}}');
@source inline('container');
@source inline('{sm:,md:,lg:,xl:,}prose{-base,-sm,-lg,-xl,-2xl,}');
