.app-header {
  @apply text-white flex flex-col justify-between relative px-[1.25rem] bg-transparent;
  @apply h-[8.875rem] transition-colors duration-250 ease-in-out;

  /* transition: background-color 0.25s ease-in-out; */
}

.app-header.opaque, .app-header.fixed {
  background-color: var(--color-secondary);
  color: var(--color-secondary-content);
}

.app-header > .logo-container {
  @apply flex justify-center items-center h-[88px];
}

.app-header > .logo-container gahr-logo {
  @apply h-[1.5rem];
}

.app-header > .logo-container gahr-logo .logo {
  @apply h-full;
}

.app-header .cta-reserve {
  @apply absolute top-5 right-5;
}

.app-header nav li:hover {
  @apply cursor-pointer;
  color: var(--color-primary);
}

.app-header .primary-nav {
  @apply flex justify-center items-center h-[54px];
  border-top: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
}

.app-header.opaque .primary-nav {
  @apply border-b-0;
  border-top: 1px solid var(--color-primary);
}

.app-header .primary-nav > li {
  @apply relative flex items-center uppercase h-full py-0 px-5;
}

.app-header .secondaryNavList {
  @apply hidden absolute top-[54px] left-0 p-5;
  background-color: var(--color-secondary);
  color: #000000;
}

.app-header .secondaryNavList.active {
  @apply block;
}

.app-header .secondaryNavList li {
  @apply text-nowrap mb-[0.75rem];
}
.app-header .secondaryNavList li:last-of-type {
  @apply mb-[0];
}

/* Collpsed Fixed Header */
.app-header.fixed {
  @apply fixed flex-row w-full top-0 left-0 h-[74px];
}

.app-header.fixed > .logo-container {
  @apply justify-start flex-1 h-full;
}

.app-header.fixed > .logo-container gahr-logo .logo {
  @apply w-auto;
}

.app-header.fixed .cta-reserve {
  @apply static flex items-center flex-1 justify-end;
}

.app-header.fixed .primary-nav {
  @apply flex-1 h-full border-none;
}

.app-header.fixed .secondaryNavList {
  @apply top-[74px];
}

/* Mobile Header */
.app-header.mobile {
  @apply flex-row items-start h-[101px] p-4 border-b-0 h-auto;
}

.app-header.mobile .logo-container {
  @apply flex-1 flex-row items-start h-full p-0;
}

.app-header.mobile > .logo-container gahr-logo {
  @apply h-[4.25rem];
}

.app-header.mobile > .logo-container gahr-logo .logo {
  @apply w-full;
}

.app-header.mobile > nav {
  @apply flex-1;
  order: -1;
}

.app-header.mobile .cta-reserve {
  @apply static flex flex-1 justify-end items-center;
}

.app-header.mobile .icon-hamburger {
  @apply flex flex-col justify-center items-center block h-[38px] w-[38px];
  border: 1px solid rgba(255,255, 255, .5);
}

.app-header.mobile .icon-hamburger > div {
  @apply h-[1px] w-[20px];
  border-top: 1px solid rgba(255,255, 255, 1);
}

.app-header.mobile .icon-hamburger > div:first-of-type {
  @apply mb-1;
}

.app-header.mobile .primary-nav {
  @apply hidden absolute top-[6.25rem] left-0 w-full h-auto border-none;
  background-color: var(--color-secondary);
}

.app-header.mobile .primary-nav > li {
  @apply block static normal-case h-auto text-2xl py-2 px-5;
}

.app-header.mobile .primary-nav.active {
  @apply block;
  background-color: var(--color-secondary);
}

.app-header.mobile.opaque .icon-hamburger {
  border: 1px solid var(--color-primary);
}

.app-header.mobile.opaque .icon-hamburger > svg {
  color: var(--color-primary);
}

.app-header.mobile.fixed .icon-hamburger {
  border: 1px solid var(--color-primary);
}

.app-header.mobile.fixed .icon-hamburger > svg {
  color: var(--color-primary);
}

.app-header.mobile .secondaryNavList {
  @apply static py-2 px-0;
  background-color: var(--color-secondary);
  color: #000000;
}

.app-header.mobile .secondaryNavList > li {
  @apply mb-2 text-xl;
}
