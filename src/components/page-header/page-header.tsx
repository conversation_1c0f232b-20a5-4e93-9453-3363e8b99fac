import { Component, Prop, h, Host, State, Element, Listen } from '@stencil/core';
import defaultNav from './nav.json';

export interface NavItem {
  label: string;
  url?: string;
  nav?: NavItem[];
}

@Component({
  tag: 'gahr-page-header',
  styleUrl: 'page-header.css',
  shadow: false,
})
export class PageHeader {
  @Element() el: HTMLElement;
  private observer: IntersectionObserver;

  @Prop() primaryNav: NavItem[] = defaultNav;
  @State() activeSecondaryNav: NavItem;
  @State() fixed: boolean = false;

  // Mobile State
  @State() isMobile: boolean;
  @State() isPrimaryNavOpen: boolean;

  componentWillLoad() {
    this.isMobile = window.innerWidth <= 768;
  }

  // Listener for window resize events
  @Listen('resize', { target: 'window' })
  handleResize() {
    this.isMobile = window.innerWidth <= 768;
  }

  componentDidLoad() {
    this.observer = new IntersectionObserver(this.handleIntersection.bind(this), {
      root: null, // observes intersection with the viewport
      threshold: 0.1, // trigger when 10% of the element is visible
    });

    this.observer.observe(this.el);
  }

  disconnectedCallback() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }

  handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach((entry) => {
      if (!entry.isIntersecting) {
        // Element is out of view
        this.fixed = true;
      } else {
        // Element is in view
        this.fixed = false;
      }
    });
  }

  render() {
    const openSecondaryNav = (nav:NavItem) => {
      if (this.isMobile) return;
      this.activeSecondaryNav = nav;
    }

    const closeSecondaryNav = () => {
      if (this.isMobile) return;
      this.activeSecondaryNav = null;
    }

    // Mobile functions
    const togglePrimaryNav = () => {
      if (!this.isMobile) return;
      this.isPrimaryNavOpen = !this.isPrimaryNavOpen;
    }

    const toggleSecondaryNav = (nav:NavItem) => {
      if (!this.isMobile) return;
      if (nav === this.activeSecondaryNav) {
        this.activeSecondaryNav = null;
      } else {
        this.activeSecondaryNav = nav;
      }
    }

    const renderNav = () => (
      <nav>
        {this.isMobile && (
          // TODO Change this to a button
          <div class="icon-hamburger" onClick={() => togglePrimaryNav()}>
            {!this.isPrimaryNavOpen && (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 9h16.5m-16.5 6.75h16.5" />
              </svg>
            )}
            {this.isPrimaryNavOpen && (
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
              </svg>
            )}
          </div>
        )}

        <ul class={`primary-nav ${this.isPrimaryNavOpen ? 'active' : ''}`}>
          {this.primaryNav.map((navItem:NavItem) => {
            const hasUrl = navItem.url;
            const hasSecondaryNav = navItem.nav;

            if (hasUrl) {
              return  (
                <li><a href={navItem.url}>{navItem.label}</a></li>
              )
            }

            // Render Secondary even though we don't show initially
            if (hasSecondaryNav) {
              return (
                <li
                  onMouseEnter={() => openSecondaryNav(navItem)}
                  onMouseLeave={() => closeSecondaryNav()}
                  onClick={() => toggleSecondaryNav(navItem)}
                >
                  {navItem.label} {this.isMobile && (
                    <span class={`inline-block ${this.activeSecondaryNav === navItem ? 'rotate-180' : ''}`}>
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 15.75 7.5-7.5 7.5 7.5" />
                      </svg>
                    </span>
                  )}

                  <ul class={`secondaryNavList ${this.activeSecondaryNav === navItem ? 'active' : ''}`}>
                    {navItem.nav.map((secondaryNavItem) => (
                      <li>{secondaryNavItem.label}</li>
                    ))}
                  </ul>
                </li>
              )
            }

            return null;
          })}
        </ul>
      </nav>
    )

    return (
      <Host>
        <div class={`
          app-header
          ${this.fixed ? 'fixed' : ''}
          ${this.activeSecondaryNav || this.isPrimaryNavOpen ? 'opaque' : ''}
          ${this.isMobile ? 'mobile' : ''}
        `}>
          <div class="logo-container">
            {/* Link to home? */}
            <gahr-logo
              class={`
                ${this.isMobile ? 'mobile' : ''}
                ${this.fixed || this.activeSecondaryNav || this.isPrimaryNavOpen ? 'primary' : 'secondary'}
              `}
            />
          </div>


          {renderNav()}

          <div class="cta-reserve">
            <button>Reserve</button>
          </div>
        </div>
      </Host>
    );
  }
}
