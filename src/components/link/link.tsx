import { Component, h, Host, Prop } from '@stencil/core';

@Component({
  tag: 'gahr-link',
  styleUrl: 'link.css',
  shadow: false,
})
export class Link {

  /**
   * Link Href
   * This is the URL that the link points to.
   */
  @Prop() href: string;

  /**
   * Link Type
   * This is the type of link to render.
   */
  @Prop() linkType: string;

  /**
   * Link Class
   * This is the class applied to the link element.
   */
  @Prop() linkClass: string;

  getLinkClass() : string {

    let cls = 'link';

    switch (this.linkType) {
      case 'primary':
        cls += ' link-primary';
        break;
      case 'secondary':
        cls += ' link-secondary';
        break;
      case 'tertiary':
        cls += ' link-tertiary';
        break;
    }

    if (this.linkClass) {
      cls += ` ${this.linkClass}`;
    }

    return cls;

  }

  render() {
    return (
      <Host>
        <a href={this.href} class={this.getLinkClass()}><slot /></a>
      </Host>
    );
  }
}