import { newSpecPage } from '@stencil/core/testing';
import { Logo } from './logo';

describe('gahr-logo', () => {
  it('renders with custom class', async () => {
    const page = await newSpecPage({
      components: [Logo],
      html: `<gahr-logo class="primary custom-class"></gahr-logo>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-logo class="primary custom-class" role="img">
        <span class="logo"></span>
      </gahr-logo>
    `);
  });

  it('renders with primary variant', async () => {
    const page = await newSpecPage({
      components: [Logo],
      html: `<gahr-logo class="primary"></gahr-logo>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-logo class="primary" role="img" >
        <span class="logo"></span>
      </gahr-logo>
    `);
  });

  it('renders with secondary variant', async () => {
    const page = await newSpecPage({
      components: [Logo],
      html: `<gahr-logo class="secondary"></gahr-logo>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-logo class="secondary" role="img">
        <span class="logo"></span>
      </gahr-logo>
    `);
  });

  it('has proper accessibility attributes', async () => {
    const page = await newSpecPage({
      components: [Logo],
      html: `<gahr-logo></gahr-logo>`,
    });
    
    expect(page.root!.getAttribute('role')).toBe('img');
  }); 

  describe('component structure', () => {
    it('contains a span with logo class', async () => {
      const page = await newSpecPage({
        components: [Logo],
        html: `<gahr-logo></gahr-logo>`,
      });
      
      const logoSpan = page.root!.querySelector('.logo');
      expect(logoSpan).not.toBeNull();
      expect(logoSpan!.tagName).toBe('SPAN');
    });
  });
});
