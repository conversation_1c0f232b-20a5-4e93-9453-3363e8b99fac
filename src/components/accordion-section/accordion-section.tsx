import { Component, h, Host, Prop } from '@stencil/core';

@Component({
  tag: 'gahr-accordion-section',
  styleUrl: 'accordion-section.css',
  shadow: false,
})
export class AccordionSection {

  /**
   * Accordion Name
   * This is used to group the accordion sections together.
   */
  @Prop() accordionName: string;

  /**
   * Section Title
   * This is the title of the accordion section that will be displayed.
   */
  @Prop() sectionTitle: string;

  /**
   * Is Open
   * This determines if the accordion section is open by default.
   */
  @Prop() isOpen: boolean;

  render() {
    return (
      <Host>
        <div class="collapse collapse-arrow join-item border-base-300 border">
          <input type="checkbox" name={this.accordionName} checked={this.isOpen} />
          <div class="collapse-title font-semibold">{this.sectionTitle}</div>
          <div class="collapse-content text-sm">
            <slot />
          </div>
        </div>
      </Host>
    );
  }
}