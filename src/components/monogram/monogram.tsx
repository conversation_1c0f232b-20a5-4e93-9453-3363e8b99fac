import { Component, h, Host, Prop } from '@stencil/core';

@Component({
  tag: 'gahr-monogram',
  styleUrl: 'monogram.css',
  shadow: false,
})
export class Monogram {
  /**
   * CSS class to apply to the monogram
   */
  @Prop() class?: string;

  /**
   * Alt text for accessibility
   */
  @Prop() alt?: string = 'Monogram';

  render() {
    return (
      <Host
        class={this.class}
        role="img"
        aria-label={this.alt}
      >
        <span class="monogram" />
      </Host>
    );
  }
}
