/* Monogram Component Styles */

gahr-monogram {
  @apply inline-block w-full h-auto;
}

/* CSS-mode rendering */
gahr-monogram .monogram {
  display: block;
  width: 100%;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

/* Brand-specific background assets and aspect ratios */
gahr-monogram[data-theme="ga"] .monogram {
  background-image: url('/assets/ga/monogram-ga-white.svg');
  aspect-ratio: 1 / 1; /* Adjust based on actual asset dimensions */
}

gahr-monogram[data-theme="la-slc"] .monogram {
  background-image: url('/assets/la-slc/monogram-la-slc-white.svg');
  aspect-ratio: 1 / 1; /* Adjust based on actual asset dimensions */
}

/* Default to GA brand if no data-theme is present */
gahr-monogram:not([data-theme]) .monogram {
  background-image: url('/assets/ga/monogram-ga-white.svg');
  aspect-ratio: 1 / 1;
}