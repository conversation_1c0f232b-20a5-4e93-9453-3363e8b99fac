import { newSpecPage } from '@stencil/core/testing';
import { Monogram } from './monogram';

describe('gahr-monogram', () => {
  it('renders with default props', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram></gahr-monogram>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-monogram role="img" aria-label="Monogram">
        <span class="monogram"></span>
      </gahr-monogram>
    `);
  });

  it('renders with custom class', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram class="custom-class"></gahr-monogram>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-monogram class="custom-class" role="img" aria-label="Monogram">
        <span class="monogram"></span>
      </gahr-monogram>
    `);
  });

  it('renders with custom alt text', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram alt="Custom Alt Text"></gahr-monogram>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-monogram alt="Custom Alt Text" aria-label="Custom Alt Text" role="img">
        <span class="monogram"></span>
      </gahr-monogram>
    `);
  });

  it('renders with both custom class and alt text', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram class="my-monogram" alt="Brand Logo"></gahr-monogram>`,
    });
    
    expect(page.root).toEqualHtml(`
      <gahr-monogram class="my-monogram" alt="Brand Logo" aria-label="Brand Logo" role="img">
        <span class="monogram"></span>
      </gahr-monogram>
    `);
  });

  it('has proper accessibility attributes', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram></gahr-monogram>`,
    });
    
    expect(page.root!.getAttribute('role')).toBe('img');
    expect(page.root!.getAttribute('aria-label')).toBe('Monogram');
  });

  it('uses custom aria-label when alt prop is provided', async () => {
    const page = await newSpecPage({
      components: [Monogram],
      html: `<gahr-monogram alt="Company Logo"></gahr-monogram>`,
    });
    
    expect(page.root!.getAttribute('aria-label')).toBe('Company Logo');
  });

  describe('prop validation', () => {
    it('defaults alt to "Monogram" when not specified', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram></gahr-monogram>`,
      });
      
      expect(page.root!.getAttribute('aria-label')).toBe('Monogram');
    });

    it('handles empty alt text', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram alt=""></gahr-monogram>`,
      });
      
      expect(page.root!.getAttribute('aria-label')).toBe('');
    });

    it('handles special characters in alt text', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram alt="Company's Logo & Brand"></gahr-monogram>`,
      });
      
      expect(page.root!.getAttribute('aria-label')).toBe("Company's Logo & Brand");
    });
  });

  describe('component structure', () => {
    it('contains a span with monogram class', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram></gahr-monogram>`,
      });
      
      const monogramSpan = page.root!.querySelector('.monogram');
      expect(monogramSpan).not.toBeNull();
      expect(monogramSpan!.tagName).toBe('SPAN');
    });

    it('maintains semantic structure for screen readers', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram alt="Grand America Hotel"></gahr-monogram>`,
      });
      
      // Should have img role and descriptive label
      expect(page.root!.getAttribute('role')).toBe('img');
      expect(page.root!.getAttribute('aria-label')).toBe('Grand America Hotel');
      
      // Should contain the visual element
      const monogramSpan = page.root!.querySelector('.monogram');
      expect(monogramSpan).not.toBeNull();
    });
  });

  describe('accessibility', () => {
    it('provides proper ARIA attributes for screen readers', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram alt="Hotel Brand Mark"></gahr-monogram>`,
      });
      
      expect(page.root!.getAttribute('role')).toBe('img');
      expect(page.root!.getAttribute('aria-label')).toBe('Hotel Brand Mark');
    });

    it('works with assistive technology when alt is descriptive', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram alt="Grand America Hotel monogram featuring elegant typography"></gahr-monogram>`,
      });
      
      expect(page.root!.getAttribute('aria-label')).toBe('Grand America Hotel monogram featuring elegant typography');
    });

    it('handles null or undefined alt gracefully', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram></gahr-monogram>`,
      });
      
      // Should fall back to default
      expect(page.root!.getAttribute('aria-label')).toBe('Monogram');
    });
  });

  describe('CSS integration', () => {
    it('provides styling hooks through class structure', async () => {
      const page = await newSpecPage({
        components: [Monogram],
        html: `<gahr-monogram class="custom-styling"></gahr-monogram>`,
      });
      
      // Component should have custom class
      expect(page.root!.classList.contains('custom-styling')).toBe(true);
      
      // Inner span should have monogram class for CSS targeting
      const monogramSpan = page.root!.querySelector('.monogram');
      expect(monogramSpan!.classList.contains('monogram')).toBe(true);
    });
  });
});
